{"advanced": {"gameId": 86607703, "screenWidth": 1536, "screenHeight": 864, "uiAreaWidth": 1536, "uiAreaHeight": 864, "numberFontFilename": "Acme-Regular.ttf", "fallbackFonts": "Aclonica-Regular, Aladin-Regular, Calistoga-Regular", "fontSize": 29, "mainFontFilename": "Acme-Regular.ttf", "windowOpacity": 225, "screenScale": 1, "picturesUpperLimit": 100}, "airship": {"bgm": {"name": "PerituneMaterial_Deep_Woods5_loop", "pan": 0, "pitch": 100, "volume": 80}, "characterIndex": 1, "characterName": "Vehicle", "startMapId": 122, "startX": 153, "startY": 80}, "armorTypes": ["", "General <PERSON><PERSON>", "Magic Armor", "Light Armor", "Heavy Armor", "Bracer", "Shield", "Libram", "Tech Chip", "", ""], "attackMotions": [{"type": 0, "weaponImageId": 0}, {"type": 1, "weaponImageId": 0}, {"type": 0, "weaponImageId": 0}, {"type": 1, "weaponImageId": 0}, {"type": 1, "weaponImageId": 0}, {"type": 1, "weaponImageId": 0}, {"type": 0, "weaponImageId": 0}, {"type": 2, "weaponImageId": 0}, {"type": 2, "weaponImageId": 0}, {"type": 2, "weaponImageId": 0}, {"type": 1, "weaponImageId": 0}, {"type": 0, "weaponImageId": 0}, {"type": 0, "weaponImageId": 0}], "battleBgm": {"name": "Time-to-Fight-Further", "pan": 0, "pitch": 100, "volume": 30}, "battleback1Name": "crypt2_floor", "battleback2Name": "crypt3", "battlerHue": 0, "battlerName": "Actor1_3", "battleSystem": 2, "boat": {"bgm": {"name": "Ship1", "pan": 0, "pitch": 95, "volume": 90}, "characterIndex": 1, "characterName": "Vehicle2", "startMapId": 0, "startX": 0, "startY": 0}, "currencyUnit": "G", "defeatMe": {"name": "Defeat1", "pan": 0, "pitch": 100, "volume": 50}, "editMapId": 330, "elements": ["", "\\i[81]Physical", "\\i[64]Fire", "\\i[65]Ice", "\\i[66]Thunder", "\\i[67]Water", "\\i[68]Earth", "\\i[69]Wind", "\\i[70]Light", "\\i[71]Darkness", "\\i[2192]<PERSON><PERSON>", ""], "equipTypes": ["", "Weapon", "Offhand", "Head", "Body", "Accessory", "Artifact", "", "", "", ""], "gameTitle": "Trials of Cascadia", "gameoverMe": {"name": "EG_Map_Select_01_Loop", "pan": 0, "pitch": 90, "volume": 100}, "itemCategories": [true, true, true, true], "locale": "en_US", "magicSkills": [2], "menuCommands": [true, true, true, true, true, true], "optAutosave": true, "optDisplayTp": true, "optDrawTitle": false, "optExtraExp": true, "optFloorDeath": false, "optFollowers": true, "optKeyItemsNumber": false, "optSideView": true, "optSlipDeath": false, "optTransparent": false, "partyMembers": [1, 2, 3], "ship": {"bgm": {"name": "COA_AroundTheWorld_noBrassnoDr_Loop", "pan": 0, "pitch": 100, "volume": 50}, "characterIndex": 1, "characterName": "Vehicle", "startMapId": 0, "startX": 0, "startY": 0}, "skillTypes": ["", "Skill", "Passive"], "sounds": [{"name": "EA_Pick_Item", "pan": 0, "pitch": 110, "volume": 100}, {"name": "EA_Pick_Item_Special_1", "pan": 0, "pitch": 100, "volume": 50}, {"name": "EA_UI_Select", "pan": 0, "pitch": 100, "volume": 100}, {"name": "Cancel1", "pan": 0, "pitch": 100, "volume": 50}, {"name": "Equip1", "pan": 0, "pitch": 100, "volume": 50}, {"name": "EA_Pick_Item_Special_2", "pan": 0, "pitch": 100, "volume": 100}, {"name": "EA_Pick_Item_Special_2", "pan": 0, "pitch": 100, "volume": 100}, {"name": "Battle3", "pan": 0, "pitch": 100, "volume": 50}, {"name": "Run", "pan": 0, "pitch": 100, "volume": 25}, {"name": "Attack3", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Damage5", "pan": 0, "pitch": 120, "volume": 50}, {"name": "Collapse1", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Collapse2", "pan": 0, "pitch": 100, "volume": 50}, {"name": "Collapse3", "pan": 0, "pitch": 100, "volume": 50}, {"name": "Damage5", "pan": 0, "pitch": 100, "volume": 50}, {"name": "Collapse1", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Saint1", "pan": 0, "pitch": 100, "volume": 50}, {"name": "Miss", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Evasion1", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Evasion2", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Reflection", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Coin", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Item1", "pan": 0, "pitch": 100, "volume": 60}, {"name": "Magic1", "pan": 0, "pitch": 100, "volume": 60}], "startMapId": 330, "startX": 8, "startY": 30, "switches": ["", "<PERSON><PERSON>", "Crushrux Rocks", "Parents Enter Church", "<PERSON><PERSON> Paladin <PERSON>", "SG Bosses", "UW Boss", "Soma Paralyzed", "Church Visited", "Donation", "Hidden", "Cave Guard Move", "Save my Son", "Save my Son 2", "Twilight Angel's Landing", "<PERSON><PERSON> in Bar", "Waiter <PERSON>", "Orman Beer", "<PERSON>", "Renault Appears", "Renault Defeated", "<PERSON><PERSON>", "Soldier Checking", "Renault Outside", "Autobattle On", "Enemy1", "Enemy2", "Enemy3", "Enemy4", "Enemy5", "Enemy6", "Enemy7", "Renault Vanish from Bar", "Miners Saved", "Undead Soldiers", "Undead Soldiers Defeated", "Desert Temple L1", "Desert Temple L2", "Desert Temple L3", "Desert Temple L4", "Post Renault Talk", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> from Tent", "Cross Canyon Bridge", "<PERSON><PERSON><PERSON><PERSON> Escape", "Talked to <PERSON><PERSON>", "In Smoke Forest", "In Desert", "<PERSON>ya <PERSON> Tutorial", "<PERSON><PERSON><PERSON>", "<PERSON>", "Statue1", "Statue2", "Statue3", "Statue4", "Statue5", "Statue6", "R & Z Appear Cutscene", "Goddess Eye", "<PERSON> Ai<PERSON> in Library", "<PERSON><PERSON> saved from Bandits", "Guardian Spirit Defeated", "<PERSON><PERSON>", "Muggers Defeated", "Button Pressed", "Post Battle Weather", "In Canyon", "HL Escape Music", "Title Dagger", "Treasure Door Open", "Desert Temple Open", "Desert Entrance Hider", "Fishing Success", "<PERSON><PERSON> Knocked Down", "Timed Hit Success", "Timed Hit Fail", "In Shrouded Grove", "In NW World Map", "Witness1", "Witness2", "Witness3", "Witness4", "CanScore", "Northglen Mine Lights", "In Abandoned Village", "<PERSON><PERSON>rappa Ghosts", "Time Hit Tutorial", "Guard Tutorial", "Guard Successful", "<PERSON>", "<PERSON>", "Drifter Teleport", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Minigame Transfer", "Fields <PERSON> Switch", "Beach MG Switch", "Forest MG Switch", "Mountains MG Switch", "Desert MG Switch", "<PERSON><PERSON> <PERSON>", "<global>SG Sign", "Boulders", "<global>MC Sign", "<global>MP Sign", "", "Cutscene Party", "Motor Waiting", "FiretrapSound", "Gear Bat", "QTE Success", "Airship Power Up", "Airship Power Up Lvl 2", "Airship Drone", "Airship Drone Lvl 2", "<PERSON> in Party", "<PERSON><PERSON> in Party", "<PERSON><PERSON> in Party", "<PERSON><PERSON><PERSON> in Party", "<PERSON><PERSON> in Party", "Sakura in Party", "<PERSON> in Party", "<PERSON><PERSON><PERSON> in Party", "Drifty in Party", "Subclass Card Switch", "Stolen Uniforms", "Mech Battle Switch", "<PERSON><PERSON><PERSON>", "Evasion Check", "Darksight", "CanScore", "Steam1", "Steam2", "Steam3", "Steam4", "Steam5", "Steam6", "Steam7", "Steam8", "Steam9", "Steam10", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Luxidor DE1", "Luxidor DE2", "Luxidor DE3", "Luxidor DE4", "Luxidor DE5", "Luxidor DE6", "Luxidor DE7", "Post-Investigation", "<PERSON><PERSON><PERSON><PERSON> Switch", "Hall Anomaly", "Evil Robo", "Phone Ringing", "Bomber1", "Bomber2", "Bomber3", "Bomber4", "Bomber5", "MondRoom1Complete", "MondRoom2Complete", "MondRoom3Complete", "MondRoom4Complete", "Statue Plate1", "Statue Plate2", "Statue Plate3", "Statue Plate4", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Map Scroll"], "terms": {"basic": ["Level", "Lv", "Health", "HP", "TP", "TP", "Pwr", "Pwr", "Experience", "EXP"], "commands": ["Fight", "Escape", "Attack", "Guard", "<PERSON><PERSON>", "Skill", "Equip", "Status", "Formation", "Save", "Game End", "Options", "Weapon", "Armor", "Key Item", "Equip", "Optimize", "Clear", "New Game", "Continue", null, "To Title", "Cancel", null, "Buy", "<PERSON>ll"], "params": ["Max HP", "Max MP", "Attack", "Defense", "<PERSON><PERSON>", "M.Defense", "Agility", "Luck", "Hit", "Evasion"], "messages": {"alwaysDash": "Always Dash", "commandRemember": "Command Remember", "touchUI": "Touch UI", "bgmVolume": "BGM Volume", "bgsVolume": "BGS Volume", "meVolume": "ME Volume", "seVolume": "SE Volume", "possession": "Possession", "expTotal": "Current %1", "expNext": "To Next %1", "saveMessage": "Which file would you like to save to?", "loadMessage": "Which file would you like to load?", "file": "File", "autosave": "Autosave", "partyName": "%1’s Party", "emerge": "%1 emerged!", "preemptive": "%1 got the upper hand!", "surprise": "%1 was surprised!", "escapeStart": "%1 has started to escape!", "escapeFailure": "However, it was unable to escape!", "victory": "%1 was victorious!", "defeat": "%1 was defeated.", "obtainExp": "%1 %2 received!", "obtainGold": "%1\\G found!", "obtainItem": "%1 found!", "levelUp": "%1 is now %2 %3!", "obtainSkill": "%1 learned!", "useItem": "%1 uses %2!", "criticalToEnemy": "An excellent hit!!", "criticalToActor": "A painful blow!!", "actorDamage": "%1 took %2 damage!", "actorRecovery": "%1 recovered %2 %3!", "actorGain": "%1 gained %2 %3!", "actorLoss": "%1 lost %2 %3!", "actorDrain": "%1 was drained of %2 %3!", "actorNoDamage": "%1 took no damage!", "actorNoHit": "Miss! %1 took no damage!", "enemyDamage": "%1 took %2 damage!", "enemyRecovery": "%1 recovered %2 %3!", "enemyGain": "%1 gained %2 %3!", "enemyLoss": "%1 lost %2 %3!", "enemyDrain": "%1 was drained of %2 %3!", "enemyNoDamage": "%1 took no damage!", "enemyNoHit": "Miss! %1 took no damage!", "evasion": "%1 evaded the attack!", "magicEvasion": "%1 nullified the magic!", "magicReflection": "%1 reflected the magic!", "counterAttack": "%1 made a counterattack!", "substitute": "%1 protected %2!", "buffAdd": "%1’s %2 went up!", "debuffAdd": "%1’s %2 went down!", "buffRemove": "%1’s %2 returned to normal!", "actionFailure": "There was no effect on %1!"}}, "testBattlers": [{"actorId": 1, "level": 30, "equips": [2, 121, 0, 21, 307]}, {"actorId": 6, "level": 30, "equips": [0, 161, 0, 0, 0]}, {"actorId": 10, "level": 30, "equips": [0, 0, 0, 0, 0]}, {"actorId": 0, "level": 30, "equips": [81, 0, 0, 21, 0]}], "testTroopId": 71, "title1Name": "", "title2Name": "", "titleBgm": {"name": "PerituneMaterial_Epic", "pan": 0, "pitch": 100, "volume": 80}, "titleCommandWindow": {"offsetX": 0, "offsetY": 0, "background": 2}, "variables": ["", "Monster Amount", "Killed in Oasis", "Talked to Parents", "<PERSON><PERSON> Incoming", "<PERSON><PERSON><PERSON>", "Statue Puzzle", "Flame Puzzle", "Statue Puzzle 2", "Chickens", "Miners Saved", "Safe Code", "Game Points", "Current Region", "Current Target", "Desert MG Num 1", "Desert MG Num 2", "Desert MG Num 3", "Desert MG Num 4", "Rock1 X", "Rock1 Y", "Rock2 X", "Rock2 Y", "Rock3 X", "Rock3 Y", "Rock4 X", "Rock4 Y", "Rock5 X", "Rock5 Y", "Treasure Trove 1", "Treasure Trove 2", "Treasure Trove 3", "Treasure Trove 4", "Treasure Trove 5", "Fishing Tension", "Fishing Timer", "Fishing Exp", "Fishing Level", "SC Character Selected", "Player Dice 1", "Player Dice 2", "Player Dice 3", "Player Dice 4", "Player <PERSON>ce 5", "Player <PERSON><PERSON> 6", "NPC Dice 1/RR1", "NPC Dice 2/RR2", "NPC Dice 3/RR3", "NPC Dice 4/RR4", "NPC Dice 5/RR5", "NPC Dice 6/RR6", "Hold Dice 1/RR7", "Hold Dice 2/RR8", "Hold Dice 3/RR9", "Hold Dice 4/RR10", "Hold Dice 5/RR11", "Hold Dice 6/RR12", "First Roll", "NumHeldDice", "LastSelectedChoiceIndex", "Manual Choice Change", "damageValue", "chosenElement", "EDD Player HP", "EDD Opponent HP", "EDD DEF Animation", "Hold Dice 7", "Hold Dice 8", "Hold Dice 9", "Hold Dice 10", "Hold Dice 11", "Hold Dice 12", "Player Counter Attack", "Opponent Counter Attack", "Counter Attack Value", "EDD Rule Variable", "SG <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "Snowclaw House NW", "Snowclaw House W", "Snowclaw House SW", "Snowclaw House NE", "Snowclaw House E", "Snowclaw House SE", "Safe Door NW", "Safe Door W", "Safe Door SW", "Safe Door NE", "Safe Door E", "Safe Door SE", "Arena Crowd Favor", "Arena Player HP", "Arena Opponent HP", "Enemy Vanish", "Map X", "Map Y", "Current Map", "Current Actor TP", "<PERSON>", "Or<PERSON>tch", "Aiya <PERSON>tch", "<PERSON><PERSON><PERSON>tch", "<PERSON><PERSON>tch", "<PERSON><PERSON><PERSON>wtch", "<PERSON>", "<PERSON><PERSON><PERSON>", "Arena Turn Number", "Arena Round Number", "Bar P1 Points", "Bar P2 Points", "Bar P1 Stamina", "Bar P2 Stamina", "Counter", "Random Number", "Orman HP", "Orman MaxHP", "Concentrated Attack", "Random Gold Award", "Random Equipment", "Title Screen Effect", "Store X", "Store Y", "Enemy Index", "AS Loop", "Timed Attack Button", "Timed Fail", "Timed.x", "Timed.y", "Troop Number", "Fishing Timer", "Fishing Timer 2", "Random Number 2", "AS Loop 2", "<PERSON>mina Counter", "Doom Countdown", "Reflected Bonds ID", "PartyLevel", "Meteor>Blitz Flare", "Sub Zero><PERSON><PERSON>", "Chain Lightning>Fulmination", "Ashtoreth Stacks", "Hex Trap Puzzle", "CurrentActor", "IsaacLvLbefore", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OrmanLvLbefore", "OrmanLvLafter", "AiyaLvLbefore", "AiyaLv<PERSON><PERSON>er", "MondrusLvLbefore", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er", "ZayneLvLbefore", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er", "SakuraLvLbefore", "SakuraLvLafter", "BernardLvLbefore", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KeldricLvLbefore", "<PERSON><PERSON>ric<PERSON>v<PERSON><PERSON>er", "DriftyLvLbefore", "DriftyLvLafter", "PreviousChoice1", "PreviousChoice2", "StatChoice1", "StatChoice2", "StatChoice3", "Momentum Stacks", "DrinkChoice1", "DrinkChoice2", "DrinkChoice3", "Orman Round Pts", "<PERSON><PERSON>ts", "Bow Strength", "Card 1", "Card 2", "Card 3", "Card 4", "Card 5", "Card 6", "Card 7", "Card 8", "Card 9", "Target Card", "Rounds", "Card 1 Flipped", "Card 2 Flipped", "Card 3 Flipped", "Card 4 Flipped", "Card 5 Flipped", "Card 6 Flipped", "Card 7 Flipped", "Card 8 Flipped", "Card 9 Flipped", "How Much?", "Sanity", "Shadowsight", "InputTime", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Accuracy", "Factory Part 1", "Factory Part 2", "Factory Part 3", "Motors", "Gears", "Valves", "Servos", "Selected Actor", "Fleece Gold", "Steam Pressure", "Pressure Growth", "SqrlBsh1", "SqrlBsh2", "SqrlBsh3", "SqrlBsh4", "SqrlBsh5", "SqrlBsh6", "SqrlBsh7", "SqrlBsh8", "SqrlBsh9", "SqrlBsh10", "SqrlBsh11", "SqrlBsh12", "SqrlBsh13", "SqrlHeld", "RvlSqrlMv", "RvlSqrlHeld", "SqrlScr", "RvlSqrlScr", "Box A X / Bandit1", "Box A Y / Bandit2", "Box B X / Bandit3", "Box B Y / Bandit4", "Current Aura", "<PERSON><PERSON><PERSON>", "Real Kythera", "Kythera Hits", "Last Real Kythera", "Blood Meter", "Drink in Hand", "VIP Customer", "<PERSON><PERSON>", "<PERSON><PERSON>", "Skill Mash <PERSON>", "<PERSON><PERSON> Last Element", "Ka<PERSON>mi Last Type Used", "<PERSON><PERSON><PERSON><PERSON>", "Shop Rep", "Shop Budget", "Stock Value", "Item Type", "Item ID", "Weapon Demand", "<PERSON><PERSON>", "<PERSON><PERSON>", "Base Item Price", "Sell for Less", "Sell for More", "Sell for Rand", "Rep Bonus", "Grand Total", "Final Verdict", "Assist Turns", "<PERSON><PERSON><PERSON> Drifter", "M&D Multiplier", "M&D Popup", "<Self>Bomber Blast", "MonsterCurrentRoom", "Room_Order1", "Room_Order2", "Room_Order3", "Room_Order4", "Room_Step", "Room_CorrectID", "Sound Meter", "Nightmare Gears", "Rook Knife Throws", "Rook Dice 1", "Rook Dice 2", "Rook Dice 3", "<PERSON> Slash", "", "", "", "", "", "", "", "", "", "", "<global>Damage Counter Position", "Gold in Bank", "Gold on Hand", "How Much?"], "versionId": ********, "victoryMe": {"name": "GoE - Victory Fanfare", "pan": 0, "pitch": 100, "volume": 40}, "weaponTypes": ["", "<PERSON>gger", "Sword", "Kunai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Staff", "", "", "", "Claw", "", "Spear"], "windowTone": [0, 0, 0, 0], "tileSize": 48, "optSplashScreen": false, "optMessageSkip": true, "editor": {"messageWidth1": 60, "messageWidth2": 47, "jsonFormatLevel": 1}, "faceSize": 144, "iconSize": 32}